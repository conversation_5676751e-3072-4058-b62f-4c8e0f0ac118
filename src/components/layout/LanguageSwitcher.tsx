'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

interface LanguageSwitcherProps {
  currentLang: string;
}

const LanguageSwitcher = ({ currentLang }: LanguageSwitcherProps) => {
  const pathname = usePathname();
  
  // Remove the current language prefix from the pathname
  const pathWithoutLang = pathname.replace(/^\/[a-z]{2}/, '') || '/';
  
  return (
    <div className="flex items-center space-x-2 ml-4">
      <Link
        href={`/en${pathWithoutLang}`}
        className={`px-2 py-1 rounded ${
          currentLang === "en" ? "bg-gray-200" : "hover:bg-gray-100"
        }`}
      >
        EN
      </Link>
      <Link
        href={`/sv${pathWithoutLang}`}
        className={`px-2 py-1 rounded ${
          currentLang === "sv" ? "bg-gray-200" : "hover:bg-gray-100"
        }`}
      >
        SV
      </Link>
    </div>
  );
};

export default LanguageSwitcher;
